{"total_processed": 1240, "total_replied": 1132, "total_skipped": 1261, "total_errors": 289, "technology_matches": {".net": 206, "devops engineer with aws": 56, "certified sr. data engineer": 1, "data warehousing": 1, "design engineer": 3, "oracle fusion financials consultant": 101, "qa automation engineer": 2, "network engineer": 6, "software developer engineer": 1, "software developer-java": 67, "software developer": 1, "software embedded developer": 1, "sr. automation engineer": 1, "aws": 17, "java": 154, "java developer": 134, "devops": 38, "project manager": 57, "cloud": 47, "oracle": 35, "devops engineer": 7, "react js developer": 64, "sql & powerbi developer": 65, "etl": 8, "pega": 5, "embedded": 3, "azure": 18, "data engineer": 17, "data analyst": 10, "angular": 5, "ui developer": 3, "qa lead": 1, "cisco": 8, "react": 36, "ssis": 1, "java tech lead": 1, "sap sd consultant": 1, "oracle dba": 4, "program manager": 6, ".net developer": 4, "sap fico": 2, "sap sd": 4, "aws devops": 6, "agile project manager": 1, "node": 2, "power bi developer": 1, "c++": 8, "odata": 1, "front end developer": 2, "s4 hana": 1, "sap s4 hana": 1, "devops  with aws": 20, "design": 5, "sql & powerbi": 2, "network": 7, "ui": 4, "software": 17, "datastage": 1, "qa automation": 10, "front end": 2, "power bi": 1, "sas": 3, "AI Selected": 227, "etl informatica": 1}, "technology_emails": {}, "daily_stats": {"2025-05-24": {"processed": 122, "replied": 16, "skipped": 52, "errors": 110}, "2025-05-29": {"processed": 587, "replied": 586, "skipped": 549, "errors": 66}, "2025-05-30": {"processed": 531, "replied": 530, "skipped": 660, "errors": 113}}, "hourly_stats": {"2025-05-24 01": {"processed": 100, "replied": 0, "skipped": 0, "errors": 101}, "2025-05-24 02": {"processed": 15, "replied": 9, "skipped": 18, "errors": 8}, "2025-05-24 03": {"processed": 7, "replied": 7, "skipped": 34, "errors": 0}, "2025-05-24 17": {"processed": 0, "replied": 0, "skipped": 0, "errors": 1}, "2025-05-29 19": {"processed": 100, "replied": 100, "skipped": 0, "errors": 0}, "2025-05-29 20": {"processed": 95, "replied": 95, "skipped": 105, "errors": 0}, "2025-05-29 21": {"processed": 65, "replied": 65, "skipped": 135, "errors": 0}, "2025-05-29 22": {"processed": 188, "replied": 187, "skipped": 148, "errors": 66}, "2025-05-29 23": {"processed": 139, "replied": 139, "skipped": 161, "errors": 0}, "2025-05-30 00": {"processed": 81, "replied": 81, "skipped": 0, "errors": 20}, "2025-05-30 01": {"processed": 52, "replied": 52, "skipped": 48, "errors": 0}, "2025-05-30 02": {"processed": 112, "replied": 112, "skipped": 188, "errors": 0}, "2025-05-30 03": {"processed": 66, "replied": 66, "skipped": 334, "errors": 0}, "2025-05-30 19": {"processed": 94, "replied": 93, "skipped": 16, "errors": 93}, "2025-05-30 21": {"processed": 126, "replied": 126, "skipped": 74, "errors": 0}}, "last_run": "2025-05-30 21:10:55", "success_rate": 79.66, "response_time": []}