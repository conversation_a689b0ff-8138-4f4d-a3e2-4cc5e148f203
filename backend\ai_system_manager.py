#!/usr/bin/env python3
"""
AI System Manager
Comprehensive management tool for the AI-powered resume matching system
"""

import os
import sys
import json
import time
import argparse
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("📦 Checking dependencies...")
    
    required_packages = [
        ('google.generativeai', 'google-generativeai'),
        ('flask', 'flask'),
        ('pandas', 'pandas'),
    ]
    
    missing = []
    for package, pip_name in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (install with: pip install {pip_name})")
            missing.append(pip_name)
    
    if missing:
        print(f"\n⚠️ Missing packages: {', '.join(missing)}")
        return False
    
    print("✅ All dependencies satisfied")
    return True

def check_configuration():
    """Check system configuration"""
    print("\n⚙️ Checking configuration...")
    
    config_file = "config.json"
    if not os.path.exists(config_file):
        print(f"❌ Config file not found: {config_file}")
        return False
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        required_fields = ['email', 'password', 'label']
        missing_fields = [field for field in required_fields if not config.get(field)]
        
        if missing_fields:
            print(f"❌ Missing required config fields: {', '.join(missing_fields)}")
            return False
        
        # Check API key
        api_key = config.get('gemini_api_key') or os.environ.get('GEMINI_API_KEY')
        if not api_key or api_key == 'YOUR_GEMINI_API_KEY_HERE':
            print("⚠️ Gemini API key not configured (will use local fallback)")
        else:
            print("✅ Gemini API key configured")
        
        print("✅ Configuration looks good")
        return True
        
    except Exception as e:
        print(f"❌ Error reading config: {e}")
        return False

def test_ai_matcher():
    """Test AI matcher functionality"""
    print("\n🤖 Testing AI matcher...")
    
    try:
        from ai_matcher_optimized import AIConsultantMatcher
        
        matcher = AIConsultantMatcher()
        
        # Test with sample data
        sample_job = "We need a Python developer with Django experience"
        sample_consultants = [
            {"name": "John Doe", "skills": "Python, Django, REST API", "experience": "5"},
            {"name": "Jane Smith", "skills": "Java, Spring Boot, Microservices", "experience": "7"}
        ]
        
        result = matcher.match_consultant_and_generate_reply(sample_job, sample_consultants)
        
        if result[0] and result[1]:
            print(f"✅ AI matcher working - selected: {result[0]}")
            return True
        else:
            print("❌ AI matcher test failed")
            return False
            
    except Exception as e:
        print(f"❌ AI matcher error: {e}")
        return False

def test_cache_system():
    """Test cache system"""
    print("\n💾 Testing cache system...")
    
    try:
        from cache_manager import CacheManager
        
        cache_manager = CacheManager()
        stats = cache_manager.get_cache_stats()
        
        print(f"✅ Cache system working")
        print(f"  📊 AI responses: {stats['ai_responses']['valid']}/{stats['ai_responses']['total']} valid")
        print(f"  📊 Resume analyses: {stats['resume_analysis']['valid']}/{stats['resume_analysis']['total']} valid")
        print(f"  📊 Job descriptions: {stats['job_descriptions']['valid']}/{stats['job_descriptions']['total']} valid")
        
        return True
        
    except Exception as e:
        print(f"❌ Cache system error: {e}")
        return False

def test_rate_limiter():
    """Test rate limiter"""
    print("\n🚦 Testing rate limiter...")
    
    try:
        from rate_limiter import RateLimiter
        
        limiter = RateLimiter(requests_per_minute=60, min_request_interval=1.0)
        status = limiter.get_status()
        
        print(f"✅ Rate limiter working")
        print(f"  📊 Current usage: {status['requests_last_minute']}/{status['limits']['per_minute']} per minute")
        
        return True
        
    except Exception as e:
        print(f"❌ Rate limiter error: {e}")
        return False

def cleanup_caches():
    """Clean up expired cache entries"""
    print("\n🧹 Cleaning up caches...")
    
    try:
        from cache_manager import CacheManager
        
        cache_manager = CacheManager()
        cleaned = cache_manager.cleanup_expired_entries()
        
        print(f"✅ Cleaned up {cleaned} expired cache entries")
        
        # Show updated stats
        stats = cache_manager.get_cache_stats()
        total_entries = (stats['ai_responses']['total'] + 
                        stats['resume_analysis']['total'] + 
                        stats['job_descriptions']['total'])
        
        print(f"📊 Total cache entries: {total_entries}")
        
    except Exception as e:
        print(f"❌ Cache cleanup error: {e}")

def show_system_status():
    """Show comprehensive system status"""
    print("\n📊 System Status Report")
    print("=" * 50)
    
    # Check each component
    components = [
        ("Dependencies", check_dependencies),
        ("Configuration", check_configuration),
        ("AI Matcher", test_ai_matcher),
        ("Cache System", test_cache_system),
        ("Rate Limiter", test_rate_limiter)
    ]
    
    results = {}
    for name, test_func in components:
        try:
            results[name] = test_func()
        except Exception as e:
            print(f"❌ {name} test failed: {e}")
            results[name] = False
    
    # Summary
    print("\n📋 Summary:")
    passed = sum(results.values())
    total = len(results)
    
    for name, status in results.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {name}")
    
    print(f"\n🎯 Overall: {passed}/{total} components working")
    
    if passed == total:
        print("🎉 System is fully operational!")
    elif passed >= total * 0.8:
        print("⚠️ System is mostly operational with minor issues")
    else:
        print("🚨 System has significant issues that need attention")

def install_dependencies():
    """Install missing dependencies"""
    print("\n📦 Installing dependencies...")
    
    import subprocess
    
    packages = [
        "google-generativeai==0.8.3",
        "flask==2.3.3",
        "flask-cors==4.0.0",
        "pandas==2.0.3"
    ]
    
    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} installed")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="AI System Manager")
    parser.add_argument("--status", action="store_true", help="Show system status")
    parser.add_argument("--cleanup", action="store_true", help="Clean up expired caches")
    parser.add_argument("--install", action="store_true", help="Install dependencies")
    parser.add_argument("--test", action="store_true", help="Run all tests")
    
    args = parser.parse_args()
    
    print("🚀 AI System Manager")
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if args.install:
        install_dependencies()
    elif args.cleanup:
        cleanup_caches()
    elif args.test or args.status:
        show_system_status()
    else:
        # Default: show status
        show_system_status()
        
        print("\n🔧 Available commands:")
        print("  python ai_system_manager.py --status    # Show system status")
        print("  python ai_system_manager.py --cleanup   # Clean up caches")
        print("  python ai_system_manager.py --install   # Install dependencies")
        print("  python ai_system_manager.py --test      # Run all tests")

if __name__ == "__main__":
    main()
