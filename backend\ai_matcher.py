import os
import json
import logging
import time
import hashlib
import pickle
from datetime import datetime, timedelta
import google.generativeai as genai
from typing import List, Dict, Tuple, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AIConsultantMatcher:
    def __init__(self, api_key: Optional[str] = None, cache_dir: str = "ai_cache"):
        """
        Initialize the AI Consultant Matcher with Gemini Pro

        Args:
            api_key: Gemini API key. If None, will try to load from environment or config
            cache_dir: Directory to store AI response cache
        """
        self.api_key = api_key or self._load_api_key()
        if not self.api_key:
            raise ValueError("Gemini API key not found. Please set GEMINI_API_KEY environment variable or add to config.json")

        # Configure Gemini
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')

        # Rate limiting and caching
        self.last_request_time = 0
        self.min_request_interval = 2.0  # Minimum 2 seconds between requests
        self.max_retries = 3
        self.base_retry_delay = 1.0  # Base delay for exponential backoff

        # Cache setup
        self.cache_dir = cache_dir
        self.cache_file = os.path.join(cache_dir, "ai_responses.json")
        self.cache_max_age_hours = 24  # Cache responses for 24 hours
        self._ensure_cache_dir()
        self.response_cache = self._load_cache()

        # Local matching fallback
        self.enable_local_fallback = True

        logger.info("AI Consultant Matcher initialized with caching and rate limiting")

    def _load_api_key(self) -> Optional[str]:
        """Load API key from environment variable or config file"""
        # Try environment variable first
        api_key = os.environ.get("GEMINI_API_KEY")
        if api_key:
            logger.info("Loaded Gemini API key from environment variable")
            return api_key

        # Try config.json file
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    api_key = config.get('gemini_api_key')
                    if api_key:
                        logger.info("Loaded Gemini API key from config.json")
                        return api_key
        except Exception as e:
            logger.warning(f"Error loading config.json: {e}")

        logger.warning("Gemini API key not found in environment or config")
        return None

    def _ensure_cache_dir(self):
        """Ensure cache directory exists"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
            logger.info(f"Created cache directory: {self.cache_dir}")

    def _load_cache(self) -> Dict:
        """Load response cache from file"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r') as f:
                    cache = json.load(f)
                logger.info(f"Loaded {len(cache)} cached responses")
                return cache
        except Exception as e:
            logger.warning(f"Error loading cache: {e}")
        return {}

    def _save_cache(self):
        """Save response cache to file"""
        try:
            with open(self.cache_file, 'w') as f:
                json.dump(self.response_cache, f, indent=2)
            logger.debug(f"Saved {len(self.response_cache)} responses to cache")
        except Exception as e:
            logger.error(f"Error saving cache: {e}")

    def _generate_cache_key(self, job_description: str, consultants: List[Dict]) -> str:
        """Generate a unique cache key for the request"""
        # Create a hash of the job description and consultant data
        consultant_summary = []
        for c in consultants:
            consultant_summary.append({
                'name': c.get('name', ''),
                'skills': c.get('skills', ''),
                'experience': c.get('experience', '')
            })

        cache_data = {
            'job_description': job_description[:500],  # Limit to first 500 chars
            'consultants': consultant_summary
        }

        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()

    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """Check if a cache entry is still valid"""
        try:
            cached_time = datetime.fromisoformat(cache_entry.get('timestamp', ''))
            age = datetime.now() - cached_time
            return age.total_seconds() < (self.cache_max_age_hours * 3600)
        except:
            return False

    def _rate_limit_wait(self):
        """Wait if necessary to respect rate limits"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        if time_since_last < self.min_request_interval:
            wait_time = self.min_request_interval - time_since_last
            logger.info(f"Rate limiting: waiting {wait_time:.2f} seconds")
            time.sleep(wait_time)

        self.last_request_time = time.time()

    def _format_consultant_data(self, consultants: List[Dict]) -> str:
        """Format consultant data for the AI prompt"""
        if not consultants:
            return "No consultants available."

        formatted_consultants = []
        for i, consultant in enumerate(consultants, 1):
            name = consultant.get('name', 'Unknown')
            skills = consultant.get('skills', '')
            experience = consultant.get('experience', consultant.get('years_experience', ''))
            location = consultant.get('location', '')
            visa = consultant.get('visa', consultant.get('visa_status', ''))
            relocation = consultant.get('relocation', '')

            consultant_info = f"""
Consultant #{i}:
- Name: {name}
- Skills/Technologies: {skills}
- Experience: {experience} years
- Location: {location}
- Visa Status: {visa}
- Willing to Relocate: {relocation}
"""
            formatted_consultants.append(consultant_info.strip())

        return "\n\n".join(formatted_consultants)

    def _local_fallback_match(self, job_description: str, consultants: List[Dict]) -> Tuple[Optional[str], Optional[str], Optional[Dict]]:
        """Local keyword-based matching as fallback when AI is unavailable"""
        if not consultants:
            return None, None, None

        logger.info("Using local fallback matching")

        # Extract keywords from job description
        job_keywords = set()
        job_lower = job_description.lower()

        # Common tech keywords to look for
        tech_keywords = [
            'java', 'python', 'javascript', 'react', 'angular', 'node', 'spring', 'django',
            'aws', 'azure', 'docker', 'kubernetes', 'sql', 'mongodb', 'postgresql',
            'machine learning', 'ai', 'data science', 'devops', 'microservices',
            'rest api', 'graphql', 'typescript', 'vue', 'flutter', 'ios', 'android'
        ]

        for keyword in tech_keywords:
            if keyword in job_lower:
                job_keywords.add(keyword)

        # Score consultants based on keyword matches
        consultant_scores = []
        for consultant in consultants:
            skills = consultant.get('skills', '').lower()
            score = 0
            matched_skills = []

            for keyword in job_keywords:
                if keyword in skills:
                    score += 1
                    matched_skills.append(keyword)

            if score > 0:
                consultant_scores.append({
                    'consultant': consultant,
                    'score': score,
                    'matched_skills': matched_skills
                })

        if not consultant_scores:
            # If no keyword matches, return first consultant
            consultant = consultants[0]
            message = f"I'd like to recommend {consultant.get('name', 'this consultant')} for your position. They have {consultant.get('experience', '5')} years of experience and would be a good fit for your requirements."

            response = {
                'selected_consultant_name': consultant.get('name', ''),
                'match_confidence': 'Low',
                'key_matching_skills': [],
                'email_message': message
            }

            return consultant.get('name', ''), message, response

        # Sort by score and return best match
        consultant_scores.sort(key=lambda x: x['score'], reverse=True)
        best_match = consultant_scores[0]

        consultant = best_match['consultant']
        matched_skills = best_match['matched_skills']

        # Generate simple message
        skills_text = ', '.join(matched_skills)
        message = f"I'd like to recommend {consultant.get('name', 'this consultant')} for your position. They have {consultant.get('experience', '5')} years of experience with {skills_text}, which matches your requirements well."

        confidence = 'High' if best_match['score'] >= 3 else 'Medium' if best_match['score'] >= 2 else 'Low'

        response = {
            'selected_consultant_name': consultant.get('name', ''),
            'match_confidence': confidence,
            'key_matching_skills': matched_skills,
            'email_message': message
        }

        return consultant.get('name', ''), message, response

    def _create_matching_prompt(self, job_description: str, consultant_data: str) -> str:
        """Create optimized prompt for Gemini to match consultants and generate response"""
        # Truncate job description to reduce token usage
        job_desc_truncated = job_description[:800] if len(job_description) > 800 else job_description

        prompt = f"""You are a technical recruiter. Match the best consultant to this job and write a brief email.

Job: {job_desc_truncated}

Consultants: {consultant_data}

Return JSON only:
{{
    "selected_consultant_name": "consultant name",
    "match_confidence": "High/Medium/Low",
    "key_matching_skills": ["skill1", "skill2"],
    "email_message": "Brief professional message (50-100 words) explaining why this consultant fits the role."
}}"""
        return prompt

    def match_consultant_and_generate_reply(self, job_description: str, consultants: List[Dict]) -> Tuple[Optional[str], Optional[str], Optional[Dict]]:
        """
        Use Gemini AI to match the best consultant and generate a custom reply message with caching and rate limiting

        Args:
            job_description: The job description from the email
            consultants: List of consultant dictionaries

        Returns:
            Tuple of (consultant_name, custom_message, full_ai_response)
        """
        try:
            if not consultants:
                logger.warning("No consultants provided for matching")
                return None, None, None

            # Check cache first
            cache_key = self._generate_cache_key(job_description, consultants)
            if cache_key in self.response_cache:
                cache_entry = self.response_cache[cache_key]
                if self._is_cache_valid(cache_entry):
                    logger.info("Using cached AI response")
                    cached_response = cache_entry['response']
                    return (
                        cached_response.get('selected_consultant_name'),
                        cached_response.get('email_message'),
                        cached_response
                    )
                else:
                    # Remove expired cache entry
                    del self.response_cache[cache_key]

            # Try AI matching with retry logic
            for attempt in range(self.max_retries):
                try:
                    # Rate limiting
                    self._rate_limit_wait()

                    # Format consultant data
                    consultant_data = self._format_consultant_data(consultants)

                    # Create prompt
                    prompt = self._create_matching_prompt(job_description, consultant_data)

                    logger.info(f"Sending request to Gemini AI for {len(consultants)} consultants (attempt {attempt + 1})")

                    # Generate response using Gemini
                    response = self.model.generate_content(prompt)

                    if not response or not response.text:
                        logger.error("Empty response from Gemini AI")
                        if attempt < self.max_retries - 1:
                            wait_time = self.base_retry_delay * (2 ** attempt)
                            logger.info(f"Retrying in {wait_time} seconds...")
                            time.sleep(wait_time)
                            continue
                        else:
                            break

                    # Parse JSON response
                    try:
                        # Clean the response text - remove markdown code blocks if present
                        response_text = response.text.strip()
                        if response_text.startswith('```json'):
                            response_text = response_text[7:]  # Remove ```json
                        if response_text.endswith('```'):
                            response_text = response_text[:-3]  # Remove ```
                        response_text = response_text.strip()

                        ai_response = json.loads(response_text)

                        consultant_name = ai_response.get('selected_consultant_name')
                        email_message = ai_response.get('email_message')
                        match_confidence = ai_response.get('match_confidence', 'Unknown')
                        key_skills = ai_response.get('key_matching_skills', [])

                        logger.info(f"AI selected consultant: {consultant_name} with {match_confidence} confidence")
                        logger.info(f"Key matching skills: {', '.join(key_skills)}")

                        # Cache the successful response
                        self.response_cache[cache_key] = {
                            'timestamp': datetime.now().isoformat(),
                            'response': ai_response
                        }
                        self._save_cache()

                        return consultant_name, email_message, ai_response

                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse AI response as JSON: {e}")
                        logger.error(f"Raw response: {response.text}")

                        # Fallback: try to extract consultant name and create basic message
                        return self._fallback_parsing(response.text, consultants)

                except Exception as e:
                    logger.error(f"Error in AI request (attempt {attempt + 1}): {e}")
                    if attempt < self.max_retries - 1:
                        wait_time = self.base_retry_delay * (2 ** attempt)
                        logger.info(f"Retrying in {wait_time} seconds...")
                        time.sleep(wait_time)
                    else:
                        logger.error("All AI retry attempts failed")
                        break

            # If AI fails completely, use local fallback
            if self.enable_local_fallback:
                logger.warning("AI matching failed, using local fallback")
                return self._local_fallback_match(job_description, consultants)

            return None, None, None

        except Exception as e:
            logger.error(f"Error in AI consultant matching: {e}")
            # Use local fallback as last resort
            if self.enable_local_fallback:
                return self._local_fallback_match(job_description, consultants)
            return None, None, None

    def _fallback_parsing(self, response_text: str, consultants: List[Dict]) -> Tuple[Optional[str], Optional[str], Optional[Dict]]:
        """Fallback parsing when JSON parsing fails"""
        try:
            # Try to find consultant name in the response
            for consultant in consultants:
                name = consultant.get('name', '')
                if name and name.lower() in response_text.lower():
                    # Create a basic message
                    skills = consultant.get('skills', '')
                    experience = consultant.get('experience', consultant.get('years_experience', ''))

                    basic_message = f"I'd like to recommend {name} for this position. They have {experience} years of experience with {skills}, which aligns well with your requirements. They are available immediately and would be a great fit for your team."

                    fallback_response = {
                        "selected_consultant_name": name,
                        "match_confidence": "Medium",
                        "key_matching_skills": skills.split(',')[:3] if skills else [],
                        "email_message": basic_message
                    }

                    logger.info(f"Fallback parsing selected: {name}")
                    return name, basic_message, fallback_response

            logger.warning("Fallback parsing failed to find consultant name")
            return None, None, None

        except Exception as e:
            logger.error(f"Error in fallback parsing: {e}")
            return None, None, None

    def test_connection(self) -> bool:
        """Test the connection to Gemini AI"""
        try:
            test_prompt = "Hello, please respond with 'Connection successful'"
            response = self.model.generate_content(test_prompt)

            if response and response.text:
                logger.info("Gemini AI connection test successful")
                return True
            else:
                logger.error("Gemini AI connection test failed - empty response")
                return False

        except Exception as e:
            logger.error(f"Gemini AI connection test failed: {e}")
            return False

    def clear_cache(self):
        """Clear all cached responses"""
        self.response_cache = {}
        self._save_cache()
        logger.info("Cleared AI response cache")

    def get_cache_stats(self) -> Dict:
        """Get cache statistics"""
        valid_entries = 0
        expired_entries = 0

        for cache_entry in self.response_cache.values():
            if self._is_cache_valid(cache_entry):
                valid_entries += 1
            else:
                expired_entries += 1

        return {
            'total_entries': len(self.response_cache),
            'valid_entries': valid_entries,
            'expired_entries': expired_entries,
            'cache_file': self.cache_file
        }

    def cleanup_expired_cache(self):
        """Remove expired cache entries"""
        expired_keys = []
        for key, cache_entry in self.response_cache.items():
            if not self._is_cache_valid(cache_entry):
                expired_keys.append(key)

        for key in expired_keys:
            del self.response_cache[key]

        if expired_keys:
            self._save_cache()
            logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")

# Example usage and testing
if __name__ == "__main__":
    # Test the AI matcher
    try:
        matcher = AIConsultantMatcher()

        # Test connection
        if matcher.test_connection():
            print("✅ AI Matcher initialized successfully")

            # Show cache stats
            stats = matcher.get_cache_stats()
            print(f"📊 Cache stats: {stats}")
        else:
            print("❌ AI Matcher connection failed")

    except Exception as e:
        print(f"❌ Error initializing AI Matcher: {e}")
