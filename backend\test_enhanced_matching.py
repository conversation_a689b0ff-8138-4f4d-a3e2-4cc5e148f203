#!/usr/bin/env python3
"""
Test script for enhanced AI matching with 70-80% threshold and hotlist fallback
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_job_requirement_extraction():
    """Test enhanced job requirement extraction"""
    print("🔍 Testing Job Requirement Extraction...")
    
    try:
        from ai_matcher_optimized import AIConsultantMatcher
        
        matcher = AIConsultantMatcher()
        
        # Test job descriptions with different complexity levels
        test_jobs = [
            {
                "description": """
                We are looking for a Senior Java Developer with 5+ years of experience.
                Required skills: Java, Spring Boot, Microservices, AWS
                Nice to have: Docker, Kubernetes, React
                Location: New York, NY
                """,
                "expected_exp": 5,
                "expected_must_have": ["java", "spring boot", "microservices", "aws"]
            },
            {
                "description": """
                Python Data Scientist position - 3 years minimum experience required.
                Must have: Python, Machine Learning, TensorFlow, SQL
                Preferred: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
                """,
                "expected_exp": 3,
                "expected_must_have": ["python", "machine learning", "sql"]
            },
            {
                "description": """
                Frontend Developer needed. React and JavaScript experience essential.
                At least 2 years of experience with modern web technologies.
                """,
                "expected_exp": 2,
                "expected_must_have": ["react", "javascript"]
            }
        ]
        
        for i, test_job in enumerate(test_jobs, 1):
            print(f"\n  Test {i}: Extracting requirements...")
            requirements = matcher._extract_job_requirements(test_job["description"])
            
            print(f"    📊 Found {len(requirements['technologies'])} technologies")
            print(f"    📅 Experience required: {requirements['experience_years']} years")
            print(f"    ✅ Must-have skills: {list(requirements['must_have_skills'])}")
            print(f"    💡 Nice-to-have skills: {list(requirements['nice_to_have_skills'])}")
            
            # Verify extraction accuracy
            if requirements['experience_years'] == test_job["expected_exp"]:
                print(f"    ✅ Experience extraction correct")
            else:
                print(f"    ⚠️ Experience extraction: expected {test_job['expected_exp']}, got {requirements['experience_years']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Job requirement extraction test failed: {e}")
        return False

def test_match_scoring():
    """Test the enhanced match scoring system"""
    print("\n🎯 Testing Match Scoring System...")
    
    try:
        from ai_matcher_optimized import AIConsultantMatcher
        
        matcher = AIConsultantMatcher()
        
        # Test job description
        job_description = """
        Senior Java Developer position - 5+ years experience required.
        Must have: Java, Spring Boot, Microservices, AWS
        Nice to have: Docker, Kubernetes
        """
        
        # Test consultants with different match levels
        test_consultants = [
            {
                "name": "High Match Consultant",
                "skills": "Java, Spring Boot, Microservices, AWS, Docker, Kubernetes, REST API",
                "experience": "7",
                "expected_score": "High (85%+)"
            },
            {
                "name": "Medium Match Consultant", 
                "skills": "Java, Spring Boot, AWS, Python, React",
                "experience": "5",
                "expected_score": "Medium (75-85%)"
            },
            {
                "name": "Low Match Consultant",
                "skills": "Python, Django, React, Node.js",
                "experience": "3",
                "expected_score": "Low (<70%)"
            },
            {
                "name": "Partial Match Consultant",
                "skills": "Java, Spring, MySQL, PHP",
                "experience": "4",
                "expected_score": "Medium (70-75%)"
            }
        ]
        
        # Extract job requirements
        job_requirements = matcher._extract_job_requirements(job_description)
        
        print(f"  📋 Job Requirements:")
        print(f"    Experience: {job_requirements['experience_years']} years")
        print(f"    Must-have: {list(job_requirements['must_have_skills'])}")
        print(f"    Nice-to-have: {list(job_requirements['nice_to_have_skills'])}")
        
        # Score each consultant
        for consultant in test_consultants:
            score_details = matcher._calculate_match_score(consultant, job_requirements)
            match_pct = score_details['match_percentage']
            
            print(f"\n  👤 {consultant['name']}:")
            print(f"    📊 Match Score: {match_pct:.1f}%")
            print(f"    ✅ Matched Technologies: {score_details['matched_technologies']}")
            print(f"    ❌ Missing Must-Have: {score_details['missing_must_have']}")
            print(f"    📅 Experience Match: {score_details['experience_match']}")
            print(f"    🎯 Expected: {consultant['expected_score']}")
            
            # Determine if this would meet the 70% threshold
            if match_pct >= 70:
                print(f"    ✅ WOULD SEND PROFILE (meets 70% threshold)")
            else:
                print(f"    📋 WOULD SEND HOTLIST (below 70% threshold)")
        
        return True
        
    except Exception as e:
        print(f"❌ Match scoring test failed: {e}")
        return False

def test_threshold_behavior():
    """Test the 70% threshold behavior"""
    print("\n🚦 Testing 70% Threshold Behavior...")
    
    try:
        from ai_matcher_optimized import AIConsultantMatcher
        
        matcher = AIConsultantMatcher()
        
        # Job requiring specific skills
        job_description = """
        React Developer needed - 3+ years experience.
        Must have: React, JavaScript, Node.js
        Nice to have: TypeScript, Redux
        """
        
        # Consultants with varying match levels
        consultants = [
            {
                "name": "Perfect Match",
                "skills": "React, JavaScript, Node.js, TypeScript, Redux, HTML, CSS",
                "experience": "5"
            },
            {
                "name": "Good Match",
                "skills": "React, JavaScript, Node.js, Python",
                "experience": "4"
            },
            {
                "name": "Partial Match",
                "skills": "React, JavaScript, PHP, MySQL",
                "experience": "3"
            },
            {
                "name": "Poor Match",
                "skills": "Python, Django, PostgreSQL",
                "experience": "6"
            }
        ]
        
        # Test matching
        result = matcher._local_fallback_match(job_description, consultants)
        
        if result[0]:  # If a consultant was selected
            consultant_name, message, response = result
            match_pct = response.get('match_percentage', 0)
            
            print(f"  ✅ Selected Consultant: {consultant_name}")
            print(f"  📊 Match Percentage: {match_pct:.1f}%")
            print(f"  💬 Message: {message[:100]}...")
            
            if match_pct >= 70:
                print(f"  ✅ CORRECT: Profile sent (meets threshold)")
            else:
                print(f"  ❌ ERROR: Profile sent but below threshold!")
                
        else:  # No consultant selected - should send hotlist
            response = result[2] if result[2] else {}
            best_match_pct = response.get('best_match_percentage', 0)
            
            print(f"  📋 No consultant selected (best match: {best_match_pct:.1f}%)")
            print(f"  ✅ CORRECT: Would send hotlist instead")
        
        return True
        
    except Exception as e:
        print(f"❌ Threshold behavior test failed: {e}")
        return False

def test_job_description_cleaning():
    """Test enhanced job description cleaning"""
    print("\n🧹 Testing Job Description Cleaning...")
    
    try:
        from email_handler import EmailHandler
        
        # Create a mock email handler (we just need the cleaning methods)
        handler = EmailHandler("<EMAIL>", "password", "label", [])
        
        # Test messy job description
        messy_jd = """
        From: <EMAIL>
        To: <EMAIL>
        Subject: Java Developer Position
        
        Hi there,
        
        We have an urgent requirement for a Java Developer.
        
        Requirements:
        - 5+ years Java experience
        - Spring Boot knowledge
        - AWS experience preferred
        
        Please let me know if you have suitable candidates.
        
        Best regards,
        John Smith
        
        This email is confidential and may contain privileged information.
        ________________________________
        
        > Original message from client:
        > We need someone ASAP for our Java project
        """
        
        cleaned_jd = handler._clean_job_description(messy_jd)
        
        print(f"  📝 Original length: {len(messy_jd)} characters")
        print(f"  ✨ Cleaned length: {len(cleaned_jd)} characters")
        print(f"  📋 Cleaned content preview:")
        print(f"    {cleaned_jd[:200]}...")
        
        # Check if email headers and signatures were removed
        if "from:" not in cleaned_jd.lower() and "confidential" not in cleaned_jd.lower():
            print(f"  ✅ Email headers and signatures removed")
        else:
            print(f"  ⚠️ Some email artifacts may remain")
        
        return True
        
    except Exception as e:
        print(f"❌ Job description cleaning test failed: {e}")
        return False

def main():
    """Run all enhanced matching tests"""
    print("🚀 Enhanced AI Matching System Tests")
    print("=" * 50)
    
    tests = [
        ("Job Requirement Extraction", test_job_requirement_extraction),
        ("Match Scoring System", test_match_scoring),
        ("70% Threshold Behavior", test_threshold_behavior),
        ("Job Description Cleaning", test_job_description_cleaning)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All enhanced matching tests passed!")
        print("\n✅ System Features Verified:")
        print("  • Enhanced job requirement extraction")
        print("  • 70-80% match threshold enforcement")
        print("  • Automatic hotlist fallback for low matches")
        print("  • Improved job description cleaning")
    else:
        print("⚠️ Some tests failed - please review the implementation")

if __name__ == "__main__":
    main()
