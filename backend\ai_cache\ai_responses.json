{"4c78d8413342222bf9547433490940cd": {"timestamp": "2025-05-30T22:45:38.016066", "response": {"selected_consultant_name": "<PERSON>", "match_confidence": "High", "key_matching_skills": ["Java", "Spring Boot", "Microservices", "AWS"], "email_message": "Dear <PERSON>,\n\nI hope this email finds you well.\n\nI'm writing to you today regarding a Senior Java Developer position at [Company Name] in New York City. Your profile, particularly your 8 years of Java experience, proficiency in Spring Boot and Microservices, and familiarity with AWS, strongly aligns with the requirements of this role.  The position is full-time, and given your location in New York, it would be a seamless transition.\n\nPlease let me know if you're interested in learning more. I've attached the detailed job description for your review.\n\nSincerely,\n[Your Name]\n[Your Title]\n[Your Contact Information]"}}, "49fc23864d7574a8446260fbabdda398": {"timestamp": "2025-05-30T22:51:40.163539", "response": {"selected_consultant_name": "<PERSON>", "match_confidence": "Medium", "key_matching_skills": ["python", "django"], "email_message": "I'd like to recommend <PERSON> for your position. They have 5 years of experience with python, django, which matches your requirements well."}}, "309450dbc375d73ed4c94a95e4fd14a2": {"timestamp": "2025-05-30T22:56:19.953212", "response": {"selected_consultant_name": "<PERSON>", "match_confidence": "Medium", "key_matching_skills": ["sql", "azure"], "email_message": "I'd like to recommend <PERSON> for your position. They have 17 years of experience with sql, azure, which matches your requirements well."}}, "089992819f8ddb26f6b65bfa6f49863e": {"timestamp": "2025-05-30T22:56:25.746400", "response": {"selected_consultant_name": "<PERSON><PERSON><PERSON>", "match_confidence": "Low", "key_matching_skills": [], "email_message": "I'd like to recommend <PERSON><PERSON><PERSON> for your position. They have 10 years of experience and would be a good fit for your requirements."}}, "cb0c60a11a7a603ef48236af4913b8a4": {"timestamp": "2025-05-30T22:56:31.632808", "response": {"selected_consultant_name": "Konda<PERSON>", "match_confidence": "Low", "key_matching_skills": ["aws"], "email_message": "I'd like to recommend <PERSON><PERSON><PERSON> for your position. They have 12 years of experience with aws, which matches your requirements well."}}, "12965f04b19560a900c5707189ad9140": {"timestamp": "2025-05-30T22:56:37.399358", "response": {"selected_consultant_name": "<PERSON><PERSON><PERSON><PERSON>", "match_confidence": "Medium", "key_matching_skills": ["aws", "java"], "email_message": "I'd like to recommend <PERSON><PERSON><PERSON><PERSON> for your position. They have 9 years of experience with aws, java, which matches your requirements well."}}, "2840122771c0a73247f4c21c766b0f38": {"timestamp": "2025-05-30T22:56:42.421724", "response": {"selected_consultant_name": "Konda<PERSON>", "match_confidence": "Low", "key_matching_skills": ["devops"], "email_message": "I'd like to recommend <PERSON><PERSON><PERSON> for your position. They have 12 years of experience with devops, which matches your requirements well."}}, "575428d6b218809f391cc49d9b3fae5e": {"timestamp": "2025-05-30T22:56:47.434523", "response": {"selected_consultant_name": "<PERSON><PERSON><PERSON>", "match_confidence": "Low", "key_matching_skills": [], "email_message": "I'd like to recommend <PERSON><PERSON><PERSON> for your position. They have 10 years of experience and would be a good fit for your requirements."}}, "1dc29901ae3fb1adb02cd620c3cd5fb5": {"timestamp": "2025-05-30T22:56:52.899629", "response": {"selected_consultant_name": "<PERSON><PERSON><PERSON><PERSON>", "match_confidence": "Medium", "key_matching_skills": ["aws", "java"], "email_message": "I'd like to recommend <PERSON><PERSON><PERSON><PERSON> for your position. They have 9 years of experience with aws, java, which matches your requirements well."}}, "c5ea1ab60004b106b7a9a58ea6a7d856": {"timestamp": "2025-05-30T22:56:57.796229", "response": {"selected_consultant_name": "Konda<PERSON>", "match_confidence": "Low", "key_matching_skills": ["aws"], "email_message": "I'd like to recommend <PERSON><PERSON><PERSON> for your position. They have 12 years of experience with aws, which matches your requirements well."}}, "08c69a9197b4bec54550ba7a8621a52c": {"timestamp": "2025-05-30T22:57:03.150717", "response": {"selected_consultant_name": "Konda<PERSON>", "match_confidence": "Medium", "key_matching_skills": ["aws", "devops"], "email_message": "I'd like to recommend <PERSON><PERSON><PERSON> for your position. They have 12 years of experience with aws, devops, which matches your requirements well."}}, "498289452cda3788d0681ef919996a72": {"timestamp": "2025-05-30T22:57:09.010487", "response": {"selected_consultant_name": "Konda<PERSON>", "match_confidence": "Low", "key_matching_skills": ["aws"], "email_message": "I'd like to recommend <PERSON><PERSON><PERSON> for your position. They have 12 years of experience with aws, which matches your requirements well."}}, "dbc2ade54798b0937ce587fc55f5e66b": {"timestamp": "2025-05-30T22:57:14.211559", "response": {"selected_consultant_name": "<PERSON><PERSON><PERSON>", "match_confidence": "Low", "key_matching_skills": [], "email_message": "I'd like to recommend <PERSON><PERSON><PERSON> for your position. They have 10 years of experience and would be a good fit for your requirements."}}, "d8a9982d760d04ce082a2c7db62d9ef0": {"timestamp": "2025-05-30T22:57:19.698613", "response": {"selected_consultant_name": "<PERSON>", "match_confidence": "Low", "key_matching_skills": ["sql"], "email_message": "I'd like to recommend <PERSON> for your position. They have 17 years of experience with sql, which matches your requirements well."}}, "051dbbd8d177a154db169d5d50cca139": {"timestamp": "2025-05-30T22:57:25.119664", "response": {"selected_consultant_name": "<PERSON><PERSON><PERSON>", "match_confidence": "Low", "key_matching_skills": ["react"], "email_message": "I'd like to recommend <PERSON><PERSON><PERSON> for your position. They have 10 years of experience with react, which matches your requirements well."}}, "70d1a2cadda9dcbf5d6bf2a3d59be7c5": {"timestamp": "2025-05-30T22:57:30.799031", "response": {"selected_consultant_name": "<PERSON><PERSON><PERSON>", "match_confidence": "Low", "key_matching_skills": [], "email_message": "I'd like to recommend <PERSON><PERSON><PERSON> for your position. They have 10 years of experience and would be a good fit for your requirements."}}, "ca691e779797244d1d34aa30adbb9869": {"timestamp": "2025-05-30T22:57:36.556760", "response": {"selected_consultant_name": "<PERSON>", "match_confidence": "Low", "key_matching_skills": ["sql"], "email_message": "I'd like to recommend <PERSON> for your position. They have 17 years of experience with sql, which matches your requirements well."}}, "3346c7a99f5ef3fc40b882db04a109da": {"timestamp": "2025-05-30T22:57:42.631098", "response": {"selected_consultant_name": "<PERSON><PERSON><PERSON>", "match_confidence": "Low", "key_matching_skills": [], "email_message": "I'd like to recommend <PERSON><PERSON><PERSON> for your position. They have 10 years of experience and would be a good fit for your requirements."}}, "73973a7d97db86d02c51060a9eb0685c": {"timestamp": "2025-05-30T22:57:48.963197", "response": {"selected_consultant_name": "<PERSON><PERSON><PERSON>", "match_confidence": "Low", "key_matching_skills": [], "email_message": "I'd like to recommend <PERSON><PERSON><PERSON> for your position. They have 10 years of experience and would be a good fit for your requirements."}}}