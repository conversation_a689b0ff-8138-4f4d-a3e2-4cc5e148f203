#!/usr/bin/env python3
"""
Quick test for AI matcher functionality
"""

import sys
import os

def test_import():
    """Test if AI matcher can be imported"""
    try:
        from ai_matcher import AIConsultantMatcher
        print("✅ AI Matcher imported successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import AI Matcher: {e}")
        return False

def test_initialization():
    """Test AI matcher initialization"""
    try:
        from ai_matcher import AIConsultantMatcher
        matcher = AIConsultantMatcher()
        print("✅ AI Matcher initialized successfully")
        
        # Test cache stats
        stats = matcher.get_cache_stats()
        print(f"📊 Cache stats: {stats}")
        
        return True
    except Exception as e:
        print(f"❌ Failed to initialize AI Matcher: {e}")
        return False

def test_local_fallback():
    """Test local fallback matching"""
    try:
        from ai_matcher import AIConsultantMatcher
        matcher = AIConsultantMatcher()
        
        # Test data
        job_description = "We need a Java developer with Spring Boot experience"
        consultants = [
            {
                "name": "<PERSON>",
                "skills": "Java, Spring Boot, Microservices",
                "experience": "8"
            },
            {
                "name": "<PERSON>", 
                "skills": "Python, Django, React",
                "experience": "5"
            }
        ]
        
        # Test local fallback
        name, message, response = matcher._local_fallback_match(job_description, consultants)
        
        if name and message:
            print(f"✅ Local fallback test successful")
            print(f"📋 Selected: {name}")
            print(f"💬 Message: {message[:50]}...")
            return True
        else:
            print("❌ Local fallback test failed")
            return False
            
    except Exception as e:
        print(f"❌ Local fallback test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Quick AI Matcher Test\n")
    
    tests = [
        ("Import Test", test_import),
        ("Initialization Test", test_initialization),
        ("Local Fallback Test", test_local_fallback)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        if test_func():
            passed += 1
        
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("⚠️ Some tests failed")

if __name__ == "__main__":
    main()
