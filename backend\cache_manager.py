#!/usr/bin/env python3
"""
Cache Management Utility for AI Matcher
Handles caching of AI responses, resume parsing, and job description analysis
"""

import os
import json
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CacheManager:
    def __init__(self, cache_dir: str = "ai_cache"):
        """
        Initialize cache manager
        
        Args:
            cache_dir: Directory to store cache files
        """
        self.cache_dir = cache_dir
        self.ai_responses_file = os.path.join(cache_dir, "ai_responses.json")
        self.resume_analysis_file = os.path.join(cache_dir, "resume_analysis.json")
        self.job_descriptions_file = os.path.join(cache_dir, "job_descriptions.json")
        
        # Cache settings
        self.ai_cache_max_age_hours = 24
        self.resume_cache_max_age_hours = 168  # 1 week
        self.job_cache_max_age_hours = 72  # 3 days
        
        self._ensure_cache_dir()
        
        # Load all caches
        self.ai_responses = self._load_cache_file(self.ai_responses_file)
        self.resume_analysis = self._load_cache_file(self.resume_analysis_file)
        self.job_descriptions = self._load_cache_file(self.job_descriptions_file)
        
        logger.info(f"Cache manager initialized with {len(self.ai_responses)} AI responses, "
                   f"{len(self.resume_analysis)} resume analyses, "
                   f"{len(self.job_descriptions)} job descriptions")
    
    def _ensure_cache_dir(self):
        """Ensure cache directory exists"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
            logger.info(f"Created cache directory: {self.cache_dir}")
    
    def _load_cache_file(self, file_path: str) -> Dict:
        """Load cache from a specific file"""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Error loading cache file {file_path}: {e}")
        return {}
    
    def _save_cache_file(self, file_path: str, cache_data: Dict):
        """Save cache to a specific file"""
        try:
            with open(file_path, 'w') as f:
                json.dump(cache_data, f, indent=2)
            logger.debug(f"Saved cache to {file_path}")
        except Exception as e:
            logger.error(f"Error saving cache file {file_path}: {e}")
    
    def _generate_hash(self, data: Any) -> str:
        """Generate a hash for cache key"""
        if isinstance(data, dict):
            data_str = json.dumps(data, sort_keys=True)
        else:
            data_str = str(data)
        return hashlib.md5(data_str.encode()).hexdigest()
    
    def _is_cache_valid(self, cache_entry: Dict, max_age_hours: int) -> bool:
        """Check if a cache entry is still valid"""
        try:
            cached_time = datetime.fromisoformat(cache_entry.get('timestamp', ''))
            age = datetime.now() - cached_time
            return age.total_seconds() < (max_age_hours * 3600)
        except:
            return False
    
    # AI Response Caching
    def get_ai_response(self, job_description: str, consultants: List[Dict]) -> Optional[Dict]:
        """Get cached AI response if available and valid"""
        cache_key = self._generate_hash({
            'job_description': job_description[:500],
            'consultants': [{'name': c.get('name', ''), 'skills': c.get('skills', '')} for c in consultants]
        })
        
        if cache_key in self.ai_responses:
            cache_entry = self.ai_responses[cache_key]
            if self._is_cache_valid(cache_entry, self.ai_cache_max_age_hours):
                logger.info("Using cached AI response")
                return cache_entry['response']
            else:
                # Remove expired entry
                del self.ai_responses[cache_key]
                self._save_cache_file(self.ai_responses_file, self.ai_responses)
        
        return None
    
    def cache_ai_response(self, job_description: str, consultants: List[Dict], response: Dict):
        """Cache an AI response"""
        cache_key = self._generate_hash({
            'job_description': job_description[:500],
            'consultants': [{'name': c.get('name', ''), 'skills': c.get('skills', '')} for c in consultants]
        })
        
        self.ai_responses[cache_key] = {
            'timestamp': datetime.now().isoformat(),
            'response': response
        }
        
        self._save_cache_file(self.ai_responses_file, self.ai_responses)
        logger.debug("Cached AI response")
    
    # Resume Analysis Caching
    def get_resume_analysis(self, resume_path: str) -> Optional[Dict]:
        """Get cached resume analysis if available and valid"""
        # Use file modification time and path as cache key
        try:
            file_stat = os.stat(resume_path)
            cache_key = self._generate_hash({
                'path': resume_path,
                'mtime': file_stat.st_mtime,
                'size': file_stat.st_size
            })
            
            if cache_key in self.resume_analysis:
                cache_entry = self.resume_analysis[cache_key]
                if self._is_cache_valid(cache_entry, self.resume_cache_max_age_hours):
                    logger.debug(f"Using cached resume analysis for {resume_path}")
                    return cache_entry['analysis']
                else:
                    del self.resume_analysis[cache_key]
                    self._save_cache_file(self.resume_analysis_file, self.resume_analysis)
        except OSError:
            pass
        
        return None
    
    def cache_resume_analysis(self, resume_path: str, analysis: Dict):
        """Cache resume analysis"""
        try:
            file_stat = os.stat(resume_path)
            cache_key = self._generate_hash({
                'path': resume_path,
                'mtime': file_stat.st_mtime,
                'size': file_stat.st_size
            })
            
            self.resume_analysis[cache_key] = {
                'timestamp': datetime.now().isoformat(),
                'analysis': analysis
            }
            
            self._save_cache_file(self.resume_analysis_file, self.resume_analysis)
            logger.debug(f"Cached resume analysis for {resume_path}")
        except OSError as e:
            logger.warning(f"Could not cache resume analysis for {resume_path}: {e}")
    
    # Job Description Caching
    def get_job_description_analysis(self, job_description: str) -> Optional[Dict]:
        """Get cached job description analysis if available and valid"""
        cache_key = self._generate_hash(job_description)
        
        if cache_key in self.job_descriptions:
            cache_entry = self.job_descriptions[cache_key]
            if self._is_cache_valid(cache_entry, self.job_cache_max_age_hours):
                logger.debug("Using cached job description analysis")
                return cache_entry['analysis']
            else:
                del self.job_descriptions[cache_key]
                self._save_cache_file(self.job_descriptions_file, self.job_descriptions)
        
        return None
    
    def cache_job_description_analysis(self, job_description: str, analysis: Dict):
        """Cache job description analysis"""
        cache_key = self._generate_hash(job_description)
        
        self.job_descriptions[cache_key] = {
            'timestamp': datetime.now().isoformat(),
            'analysis': analysis
        }
        
        self._save_cache_file(self.job_descriptions_file, self.job_descriptions)
        logger.debug("Cached job description analysis")
    
    # Cache Management
    def cleanup_expired_entries(self):
        """Remove all expired cache entries"""
        cleaned = 0
        
        # Clean AI responses
        expired_keys = []
        for key, entry in self.ai_responses.items():
            if not self._is_cache_valid(entry, self.ai_cache_max_age_hours):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.ai_responses[key]
            cleaned += 1
        
        if expired_keys:
            self._save_cache_file(self.ai_responses_file, self.ai_responses)
        
        # Clean resume analyses
        expired_keys = []
        for key, entry in self.resume_analysis.items():
            if not self._is_cache_valid(entry, self.resume_cache_max_age_hours):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.resume_analysis[key]
            cleaned += 1
        
        if expired_keys:
            self._save_cache_file(self.resume_analysis_file, self.resume_analysis)
        
        # Clean job descriptions
        expired_keys = []
        for key, entry in self.job_descriptions.items():
            if not self._is_cache_valid(entry, self.job_cache_max_age_hours):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.job_descriptions[key]
            cleaned += 1
        
        if expired_keys:
            self._save_cache_file(self.job_descriptions_file, self.job_descriptions)
        
        if cleaned > 0:
            logger.info(f"Cleaned up {cleaned} expired cache entries")
        
        return cleaned
    
    def clear_all_caches(self):
        """Clear all caches"""
        self.ai_responses = {}
        self.resume_analysis = {}
        self.job_descriptions = {}
        
        self._save_cache_file(self.ai_responses_file, self.ai_responses)
        self._save_cache_file(self.resume_analysis_file, self.resume_analysis)
        self._save_cache_file(self.job_descriptions_file, self.job_descriptions)
        
        logger.info("Cleared all caches")
    
    def get_cache_stats(self) -> Dict:
        """Get comprehensive cache statistics"""
        ai_valid = sum(1 for entry in self.ai_responses.values() 
                      if self._is_cache_valid(entry, self.ai_cache_max_age_hours))
        
        resume_valid = sum(1 for entry in self.resume_analysis.values() 
                          if self._is_cache_valid(entry, self.resume_cache_max_age_hours))
        
        job_valid = sum(1 for entry in self.job_descriptions.values() 
                       if self._is_cache_valid(entry, self.job_cache_max_age_hours))
        
        return {
            'ai_responses': {
                'total': len(self.ai_responses),
                'valid': ai_valid,
                'expired': len(self.ai_responses) - ai_valid
            },
            'resume_analysis': {
                'total': len(self.resume_analysis),
                'valid': resume_valid,
                'expired': len(self.resume_analysis) - resume_valid
            },
            'job_descriptions': {
                'total': len(self.job_descriptions),
                'valid': job_valid,
                'expired': len(self.job_descriptions) - job_valid
            },
            'cache_dir': self.cache_dir
        }

# Example usage
if __name__ == "__main__":
    cache_manager = CacheManager()
    
    # Show cache stats
    stats = cache_manager.get_cache_stats()
    print("📊 Cache Statistics:")
    for cache_type, data in stats.items():
        if isinstance(data, dict) and 'total' in data:
            print(f"  {cache_type}: {data['valid']}/{data['total']} valid entries")
    
    # Cleanup expired entries
    cleaned = cache_manager.cleanup_expired_entries()
    print(f"🧹 Cleaned up {cleaned} expired entries")
