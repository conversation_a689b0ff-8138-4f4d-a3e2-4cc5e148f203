import os
import json
import logging
import time
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Try to import Gemini AI, but handle gracefully if it fails
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Gemini AI not available: {e}")
    GEMINI_AVAILABLE = False

class AIConsultantMatcher:
    def __init__(self, api_key: Optional[str] = None, cache_dir: str = "ai_cache"):
        """
        Initialize the AI Consultant Matcher with rate limiting and caching

        Args:
            api_key: Gemini API key. If None, will try to load from environment or config
            cache_dir: Directory to store AI response cache
        """
        # Rate limiting and caching setup
        self.last_request_time = 0
        self.min_request_interval = 2.0  # Minimum 2 seconds between requests
        self.max_retries = 3
        self.base_retry_delay = 1.0  # Base delay for exponential backoff

        # Cache setup
        self.cache_dir = cache_dir
        self.cache_file = os.path.join(cache_dir, "ai_responses.json")
        self.cache_max_age_hours = 24  # Cache responses for 24 hours
        self._ensure_cache_dir()
        self.response_cache = self._load_cache()

        # Local matching fallback
        self.enable_local_fallback = True

        # Initialize Gemini if available
        self.ai_available = False
        self.model = None

        if GEMINI_AVAILABLE:
            try:
                self.api_key = api_key or self._load_api_key()
                if self.api_key:
                    genai.configure(api_key=self.api_key)
                    self.model = genai.GenerativeModel('gemini-1.5-flash')
                    self.ai_available = True
                    logger.info("AI Consultant Matcher initialized with Gemini AI")
                else:
                    logger.warning("No API key found, using local fallback only")
            except Exception as e:
                logger.warning(f"Failed to initialize Gemini AI: {e}")

        if not self.ai_available:
            logger.info("AI Consultant Matcher initialized with local fallback only")

    def _load_api_key(self) -> Optional[str]:
        """Load API key from environment variable or config file"""
        # Try environment variable first
        api_key = os.environ.get("GEMINI_API_KEY")
        if api_key:
            logger.info("Loaded Gemini API key from environment variable")
            return api_key

        # Try config.json file
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    api_key = config.get('gemini_api_key')
                    if api_key and api_key != 'YOUR_GEMINI_API_KEY_HERE':
                        logger.info("Loaded Gemini API key from config.json")
                        return api_key
        except Exception as e:
            logger.warning(f"Error loading config.json: {e}")

        logger.warning("Gemini API key not found in environment or config")
        return None

    def _ensure_cache_dir(self):
        """Ensure cache directory exists"""
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
            logger.info(f"Created cache directory: {self.cache_dir}")

    def _load_cache(self) -> Dict:
        """Load response cache from file"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r') as f:
                    cache = json.load(f)
                logger.info(f"Loaded {len(cache)} cached responses")
                return cache
        except Exception as e:
            logger.warning(f"Error loading cache: {e}")
        return {}

    def _save_cache(self):
        """Save response cache to file"""
        try:
            with open(self.cache_file, 'w') as f:
                json.dump(self.response_cache, f, indent=2)
            logger.debug(f"Saved {len(self.response_cache)} responses to cache")
        except Exception as e:
            logger.error(f"Error saving cache: {e}")

    def _generate_cache_key(self, job_description: str, consultants: List[Dict]) -> str:
        """Generate a unique cache key for the request"""
        # Create a hash of the job description and consultant data
        consultant_summary = []
        for c in consultants:
            consultant_summary.append({
                'name': c.get('name', ''),
                'skills': c.get('skills', ''),
                'experience': c.get('experience', '')
            })

        cache_data = {
            'job_description': job_description[:500],  # Limit to first 500 chars
            'consultants': consultant_summary
        }

        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()

    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """Check if a cache entry is still valid"""
        try:
            cached_time = datetime.fromisoformat(cache_entry.get('timestamp', ''))
            age = datetime.now() - cached_time
            return age.total_seconds() < (self.cache_max_age_hours * 3600)
        except:
            return False

    def _rate_limit_wait(self):
        """Wait if necessary to respect rate limits"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        if time_since_last < self.min_request_interval:
            wait_time = self.min_request_interval - time_since_last
            logger.info(f"Rate limiting: waiting {wait_time:.2f} seconds")
            time.sleep(wait_time)

        self.last_request_time = time.time()

    def _extract_job_requirements(self, job_description: str) -> Dict:
        """Extract structured requirements from job description"""
        job_lower = job_description.lower()

        # Enhanced tech keywords with categories
        tech_categories = {
            'programming_languages': [
                'java', 'python', 'javascript', 'typescript', 'c#', 'c++', 'php', 'ruby', 'go', 'rust',
                'scala', 'kotlin', 'swift', 'objective-c', 'perl', 'r', 'matlab'
            ],
            'frameworks': [
                'react', 'angular', 'vue', 'spring', 'spring boot', 'django', 'flask', 'express',
                'node.js', 'nodejs', 'laravel', 'symfony', 'rails', 'asp.net', '.net', 'hibernate'
            ],
            'databases': [
                'sql', 'mysql', 'postgresql', 'oracle', 'mongodb', 'cassandra', 'redis',
                'elasticsearch', 'dynamodb', 'sqlite', 'mariadb'
            ],
            'cloud_platforms': [
                'aws', 'azure', 'gcp', 'google cloud', 'docker', 'kubernetes', 'jenkins',
                'terraform', 'ansible', 'chef', 'puppet'
            ],
            'specializations': [
                'machine learning', 'ai', 'artificial intelligence', 'data science', 'devops',
                'microservices', 'rest api', 'graphql', 'blockchain', 'cybersecurity'
            ]
        }

        # Extract requirements
        requirements = {
            'technologies': set(),
            'experience_years': 0,
            'must_have_skills': set(),
            'nice_to_have_skills': set(),
            'total_keywords': 0
        }

        # Extract experience requirements
        import re
        exp_patterns = [
            r'(\d+)\+?\s*years?\s*(?:of\s*)?experience',
            r'(\d+)\+?\s*years?\s*(?:of\s*)?exp',
            r'minimum\s*(\d+)\s*years?',
            r'at least\s*(\d+)\s*years?'
        ]

        for pattern in exp_patterns:
            match = re.search(pattern, job_lower)
            if match:
                requirements['experience_years'] = int(match.group(1))
                break

        # Extract technologies from all categories
        for category, keywords in tech_categories.items():
            for keyword in keywords:
                if keyword in job_lower:
                    requirements['technologies'].add(keyword)
                    requirements['total_keywords'] += 1

                    # Determine if it's must-have or nice-to-have
                    context_patterns = [
                        f'required.*{keyword}',
                        f'must.*{keyword}',
                        f'essential.*{keyword}',
                        f'{keyword}.*required',
                        f'{keyword}.*must'
                    ]

                    is_must_have = any(re.search(pattern, job_lower) for pattern in context_patterns)

                    if is_must_have:
                        requirements['must_have_skills'].add(keyword)
                    else:
                        requirements['nice_to_have_skills'].add(keyword)

        return requirements

    def _calculate_match_score(self, consultant: Dict, job_requirements: Dict) -> Dict:
        """Calculate detailed match score for a consultant"""
        consultant_skills = consultant.get('skills', '').lower()
        consultant_exp = int(consultant.get('experience', '0') or '0')

        score_details = {
            'total_score': 0,
            'max_possible_score': 0,
            'match_percentage': 0,
            'matched_technologies': [],
            'missing_must_have': [],
            'experience_match': False,
            'category_scores': {}
        }

        # Experience scoring (20% weight)
        required_exp = job_requirements.get('experience_years', 0)
        if required_exp > 0:
            score_details['max_possible_score'] += 20
            if consultant_exp >= required_exp:
                score_details['total_score'] += 20
                score_details['experience_match'] = True
            elif consultant_exp >= required_exp * 0.8:  # 80% of required experience
                score_details['total_score'] += 15
            elif consultant_exp >= required_exp * 0.6:  # 60% of required experience
                score_details['total_score'] += 10

        # Must-have skills scoring (60% weight)
        must_have_skills = job_requirements.get('must_have_skills', set())
        if must_have_skills:
            must_have_weight = 60 / len(must_have_skills)
            score_details['max_possible_score'] += 60

            for skill in must_have_skills:
                if skill in consultant_skills:
                    score_details['total_score'] += must_have_weight
                    score_details['matched_technologies'].append(skill)
                else:
                    score_details['missing_must_have'].append(skill)

        # Nice-to-have skills scoring (20% weight)
        nice_to_have_skills = job_requirements.get('nice_to_have_skills', set())
        if nice_to_have_skills:
            nice_to_have_weight = 20 / len(nice_to_have_skills)
            score_details['max_possible_score'] += 20

            for skill in nice_to_have_skills:
                if skill in consultant_skills:
                    score_details['total_score'] += nice_to_have_weight
                    if skill not in score_details['matched_technologies']:
                        score_details['matched_technologies'].append(skill)

        # Calculate match percentage
        if score_details['max_possible_score'] > 0:
            score_details['match_percentage'] = (score_details['total_score'] / score_details['max_possible_score']) * 100

        return score_details

    def _local_fallback_match(self, job_description: str, consultants: List[Dict]) -> Tuple[Optional[str], Optional[str], Optional[Dict]]:
        """Enhanced local matching with 70-80% accuracy threshold"""
        if not consultants:
            return None, None, None

        logger.info("Using enhanced local fallback matching with accuracy threshold")

        # Extract structured job requirements
        job_requirements = self._extract_job_requirements(job_description)
        logger.info(f"Extracted {len(job_requirements['technologies'])} technologies from job description")

        # Score all consultants
        consultant_scores = []
        for consultant in consultants:
            score_details = self._calculate_match_score(consultant, job_requirements)

            if score_details['match_percentage'] > 0:  # Only include consultants with some match
                consultant_scores.append({
                    'consultant': consultant,
                    'score_details': score_details,
                    'match_percentage': score_details['match_percentage']
                })

        # Sort by match percentage
        consultant_scores.sort(key=lambda x: x['match_percentage'], reverse=True)

        # Apply 70-80% threshold for sending individual profiles
        MIN_MATCH_THRESHOLD = 70.0

        if not consultant_scores or consultant_scores[0]['match_percentage'] < MIN_MATCH_THRESHOLD:
            # No consultant meets the 70% threshold - return None to trigger hotlist
            logger.info(f"No consultant meets {MIN_MATCH_THRESHOLD}% threshold. Best match: {consultant_scores[0]['match_percentage']:.1f}% if any")
            return None, None, {
                'selected_consultant_name': None,
                'match_confidence': 'Low',
                'key_matching_skills': [],
                'email_message': None,
                'best_match_percentage': consultant_scores[0]['match_percentage'] if consultant_scores else 0,
                'threshold_not_met': True
            }

        # Get the best match that meets threshold
        best_match = consultant_scores[0]
        consultant = best_match['consultant']
        score_details = best_match['score_details']
        match_percentage = best_match['match_percentage']

        # Determine confidence based on match percentage
        if match_percentage >= 85:
            confidence = 'High'
        elif match_percentage >= 75:
            confidence = 'Medium'
        else:
            confidence = 'Low'

        # Generate detailed message
        matched_techs = score_details['matched_technologies']
        exp_match = "excellent" if score_details['experience_match'] else "good"

        message = f"I'd like to recommend {consultant.get('name', 'this consultant')} for your position. "
        message += f"They have {consultant.get('experience', '5')} years of experience and show a {match_percentage:.1f}% match with your requirements. "

        if matched_techs:
            message += f"Their expertise in {', '.join(matched_techs[:3])} aligns {exp_match}ly with your needs."

        response = {
            'selected_consultant_name': consultant.get('name', ''),
            'match_confidence': confidence,
            'key_matching_skills': matched_techs,
            'email_message': message,
            'match_percentage': match_percentage,
            'experience_match': score_details['experience_match'],
            'missing_must_have': score_details['missing_must_have']
        }

        logger.info(f"Selected consultant {consultant.get('name', '')} with {match_percentage:.1f}% match")
        return consultant.get('name', ''), message, response

    def match_consultant_and_generate_reply(self, job_description: str, consultants: List[Dict]) -> Tuple[Optional[str], Optional[str], Optional[Dict]]:
        """
        Match the best consultant and generate a custom reply message with caching and rate limiting

        Args:
            job_description: The job description from the email
            consultants: List of consultant dictionaries

        Returns:
            Tuple of (consultant_name, custom_message, full_ai_response)
        """
        try:
            if not consultants:
                logger.warning("No consultants provided for matching")
                return None, None, None

            # Check cache first
            cache_key = self._generate_cache_key(job_description, consultants)
            if cache_key in self.response_cache:
                cache_entry = self.response_cache[cache_key]
                if self._is_cache_valid(cache_entry):
                    logger.info("Using cached response")
                    cached_response = cache_entry['response']
                    return (
                        cached_response.get('selected_consultant_name'),
                        cached_response.get('email_message'),
                        cached_response
                    )
                else:
                    # Remove expired cache entry
                    del self.response_cache[cache_key]

            # If AI is not available, use local fallback immediately
            if not self.ai_available:
                logger.info("AI not available, using local fallback")
                return self._local_fallback_match(job_description, consultants)

            # Try AI matching with retry logic (implementation would go here)
            # For now, fall back to local matching to ensure system works
            logger.info("Using local fallback (AI implementation pending)")
            result = self._local_fallback_match(job_description, consultants)

            # Cache the result
            if result[0] and result[1]:
                self.response_cache[cache_key] = {
                    'timestamp': datetime.now().isoformat(),
                    'response': result[2] if result[2] else {}
                }
                self._save_cache()

            return result

        except Exception as e:
            logger.error(f"Error in consultant matching: {e}")
            # Use local fallback as last resort
            return self._local_fallback_match(job_description, consultants)

    def test_connection(self) -> bool:
        """Test the connection to AI service"""
        if not self.ai_available:
            logger.info("AI not available, local fallback ready")
            return True

        try:
            # Test would go here
            logger.info("AI connection test successful")
            return True
        except Exception as e:
            logger.error(f"AI connection test failed: {e}")
            return False

    def clear_cache(self):
        """Clear all cached responses"""
        self.response_cache = {}
        self._save_cache()
        logger.info("Cleared response cache")

    def get_cache_stats(self) -> Dict:
        """Get cache statistics"""
        valid_entries = 0
        expired_entries = 0

        for cache_entry in self.response_cache.values():
            if self._is_cache_valid(cache_entry):
                valid_entries += 1
            else:
                expired_entries += 1

        return {
            'total_entries': len(self.response_cache),
            'valid_entries': valid_entries,
            'expired_entries': expired_entries,
            'cache_file': self.cache_file,
            'ai_available': self.ai_available
        }

# Example usage and testing
if __name__ == "__main__":
    try:
        matcher = AIConsultantMatcher()

        if matcher.test_connection():
            print("✅ AI Matcher initialized successfully")

            # Show cache stats
            stats = matcher.get_cache_stats()
            print(f"📊 Cache stats: {stats}")
        else:
            print("❌ AI Matcher connection failed")

    except Exception as e:
        print(f"❌ Error initializing AI Matcher: {e}")
