#!/usr/bin/env python3
"""
Rate Limiter for API Requests
Implements various rate limiting strategies to prevent API quota exhaustion
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional
from collections import deque

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RateLimiter:
    def __init__(self, 
                 requests_per_minute: int = 15,
                 requests_per_hour: int = 300,
                 min_request_interval: float = 2.0,
                 max_retries: int = 3,
                 base_retry_delay: float = 1.0):
        """
        Initialize rate limiter with multiple constraints
        
        Args:
            requests_per_minute: Maximum requests per minute
            requests_per_hour: Maximum requests per hour
            min_request_interval: Minimum seconds between requests
            max_retries: Maximum retry attempts
            base_retry_delay: Base delay for exponential backoff
        """
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        self.min_request_interval = min_request_interval
        self.max_retries = max_retries
        self.base_retry_delay = base_retry_delay
        
        # Track request timestamps
        self.request_times = deque()
        self.last_request_time = 0
        
        # Track retry attempts
        self.retry_counts = {}
        
        logger.info(f"Rate limiter initialized: {requests_per_minute}/min, {requests_per_hour}/hour, "
                   f"min interval: {min_request_interval}s")
    
    def _cleanup_old_requests(self):
        """Remove request timestamps older than 1 hour"""
        current_time = time.time()
        hour_ago = current_time - 3600
        
        while self.request_times and self.request_times[0] < hour_ago:
            self.request_times.popleft()
    
    def _get_requests_in_last_minute(self) -> int:
        """Get number of requests in the last minute"""
        current_time = time.time()
        minute_ago = current_time - 60
        
        return sum(1 for req_time in self.request_times if req_time > minute_ago)
    
    def _get_requests_in_last_hour(self) -> int:
        """Get number of requests in the last hour"""
        return len(self.request_times)
    
    def _calculate_wait_time(self) -> float:
        """Calculate how long to wait before next request"""
        current_time = time.time()
        wait_times = []
        
        # Check minimum interval constraint
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            wait_times.append(self.min_request_interval - time_since_last)
        
        # Check per-minute constraint
        requests_last_minute = self._get_requests_in_last_minute()
        if requests_last_minute >= self.requests_per_minute:
            # Find the oldest request in the last minute and wait until it's 60 seconds old
            minute_ago = current_time - 60
            oldest_in_minute = min(req_time for req_time in self.request_times if req_time > minute_ago)
            wait_until = oldest_in_minute + 60
            wait_times.append(wait_until - current_time)
        
        # Check per-hour constraint
        requests_last_hour = self._get_requests_in_last_hour()
        if requests_last_hour >= self.requests_per_hour:
            # Find the oldest request and wait until it's 1 hour old
            oldest_request = self.request_times[0]
            wait_until = oldest_request + 3600
            wait_times.append(wait_until - current_time)
        
        return max(wait_times) if wait_times else 0
    
    def wait_if_needed(self, request_id: Optional[str] = None):
        """
        Wait if necessary to respect rate limits
        
        Args:
            request_id: Optional identifier for tracking retries
        """
        self._cleanup_old_requests()
        
        wait_time = self._calculate_wait_time()
        
        if wait_time > 0:
            logger.info(f"Rate limiting: waiting {wait_time:.2f} seconds")
            logger.info(f"Current usage: {self._get_requests_in_last_minute()}/{self.requests_per_minute} per minute, "
                       f"{self._get_requests_in_last_hour()}/{self.requests_per_hour} per hour")
            time.sleep(wait_time)
        
        # Record this request
        current_time = time.time()
        self.request_times.append(current_time)
        self.last_request_time = current_time
        
        # Reset retry count for successful request
        if request_id and request_id in self.retry_counts:
            del self.retry_counts[request_id]
    
    def should_retry(self, request_id: str, error: Exception) -> bool:
        """
        Determine if a request should be retried
        
        Args:
            request_id: Unique identifier for the request
            error: The error that occurred
            
        Returns:
            True if should retry, False otherwise
        """
        if request_id not in self.retry_counts:
            self.retry_counts[request_id] = 0
        
        self.retry_counts[request_id] += 1
        
        if self.retry_counts[request_id] <= self.max_retries:
            logger.info(f"Request {request_id} failed (attempt {self.retry_counts[request_id]}/{self.max_retries}): {error}")
            return True
        else:
            logger.error(f"Request {request_id} failed after {self.max_retries} attempts: {error}")
            del self.retry_counts[request_id]
            return False
    
    def get_retry_delay(self, request_id: str) -> float:
        """
        Get the delay before retrying a request (exponential backoff)
        
        Args:
            request_id: Unique identifier for the request
            
        Returns:
            Delay in seconds
        """
        attempt = self.retry_counts.get(request_id, 0)
        delay = self.base_retry_delay * (2 ** (attempt - 1))
        
        # Add some jitter to prevent thundering herd
        import random
        jitter = random.uniform(0.1, 0.5)
        
        return delay + jitter
    
    def get_status(self) -> Dict:
        """Get current rate limiter status"""
        self._cleanup_old_requests()
        
        return {
            'requests_last_minute': self._get_requests_in_last_minute(),
            'requests_last_hour': self._get_requests_in_last_hour(),
            'limits': {
                'per_minute': self.requests_per_minute,
                'per_hour': self.requests_per_hour,
                'min_interval': self.min_request_interval
            },
            'active_retries': len(self.retry_counts),
            'next_request_available_in': self._calculate_wait_time()
        }
    
    def reset(self):
        """Reset all rate limiting state"""
        self.request_times.clear()
        self.last_request_time = 0
        self.retry_counts.clear()
        logger.info("Rate limiter reset")

class AdaptiveRateLimiter(RateLimiter):
    """
    Adaptive rate limiter that adjusts limits based on API responses
    """
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.success_count = 0
        self.error_count = 0
        self.last_adjustment = time.time()
        self.adjustment_interval = 300  # 5 minutes
        
    def record_success(self):
        """Record a successful API call"""
        self.success_count += 1
        self._maybe_adjust_limits()
    
    def record_error(self, error: Exception):
        """Record an API error"""
        self.error_count += 1
        
        # If we're getting rate limit errors, be more conservative
        error_str = str(error).lower()
        if any(term in error_str for term in ['rate limit', 'quota', 'too many requests']):
            self._reduce_limits()
        
        self._maybe_adjust_limits()
    
    def _maybe_adjust_limits(self):
        """Adjust limits based on success/error ratio"""
        current_time = time.time()
        
        if current_time - self.last_adjustment < self.adjustment_interval:
            return
        
        total_requests = self.success_count + self.error_count
        if total_requests < 10:  # Need enough data
            return
        
        error_rate = self.error_count / total_requests
        
        if error_rate > 0.1:  # More than 10% errors
            self._reduce_limits()
        elif error_rate < 0.02:  # Less than 2% errors
            self._increase_limits()
        
        # Reset counters
        self.success_count = 0
        self.error_count = 0
        self.last_adjustment = current_time
    
    def _reduce_limits(self):
        """Reduce rate limits to be more conservative"""
        old_per_minute = self.requests_per_minute
        old_interval = self.min_request_interval
        
        self.requests_per_minute = max(5, int(self.requests_per_minute * 0.8))
        self.min_request_interval = min(10.0, self.min_request_interval * 1.2)
        
        logger.warning(f"Reduced rate limits: {old_per_minute} -> {self.requests_per_minute} per minute, "
                      f"{old_interval:.1f}s -> {self.min_request_interval:.1f}s interval")
    
    def _increase_limits(self):
        """Increase rate limits when performing well"""
        old_per_minute = self.requests_per_minute
        old_interval = self.min_request_interval
        
        self.requests_per_minute = min(30, int(self.requests_per_minute * 1.1))
        self.min_request_interval = max(1.0, self.min_request_interval * 0.9)
        
        logger.info(f"Increased rate limits: {old_per_minute} -> {self.requests_per_minute} per minute, "
                   f"{old_interval:.1f}s -> {self.min_request_interval:.1f}s interval")

# Example usage
if __name__ == "__main__":
    # Test basic rate limiter
    limiter = RateLimiter(requests_per_minute=5, min_request_interval=2.0)
    
    print("🚦 Testing rate limiter...")
    
    for i in range(3):
        print(f"Request {i+1}")
        limiter.wait_if_needed(f"test_request_{i}")
        print(f"Status: {limiter.get_status()}")
        print()
    
    print("✅ Rate limiter test completed")
